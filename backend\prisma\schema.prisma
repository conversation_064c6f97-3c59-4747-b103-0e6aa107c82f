// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Company {
  id        String   @id @default(cuid())
  name      String
  domain    String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  users User[]
  jobs  Job[]

  @@map("companies")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  passwordHash String   @map("password_hash")
  firstName    String?  @map("first_name")
  lastName     String?  @map("last_name")
  role         String   @default("recruiter")
  companyId    String   @map("company_id")
  avatarUrl    String?  @map("avatar_url")
  phone        String?
  bio          String?
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  company           Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)
  createdJobs       Job[]         @relation("JobCreator")
  reviewedApps      Application[] @relation("ApplicationReviewer")
  createdInterviews Interview[]   @relation("InterviewCreator")
  notifications     Notification[]
  settings          UserSettings?

  @@map("users")
}

model Job {
  id                      String   @id @default(cuid())
  title                   String
  department              String?
  location                Json? // JobLocation interface
  employmentType          String?  @map("employment_type")
  experienceLevel         String?  @map("experience_level")
  salary                  Json? // JobSalary interface
  description             String?
  responsibilities        String[]
  requiredQualifications  String[] @map("required_qualifications")
  preferredQualifications String[] @map("preferred_qualifications")
  skills                  String[]
  benefits                String?
  status                  String   @default("draft")
  visibility              String   @default("public")
  postedDate              DateTime? @map("posted_date")
  applicationDeadline     DateTime? @map("application_deadline")
  maxApplicants           Int?     @map("max_applicants")
  currentApplicants       Int      @default(0) @map("current_applicants")
  pipeline                Json? // JobPipeline interface
  source                  String   @default("internal")
  createdById             String   @map("created_by_id")
  companyId               String   @map("company_id")
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @updatedAt @map("updated_at")

  // Relations
  createdBy    User          @relation("JobCreator", fields: [createdById], references: [id])
  company      Company       @relation(fields: [companyId], references: [id], onDelete: Cascade)
  applications Application[]
  formSchemas  ApplicationFormSchema[]

  @@map("jobs")
}

model Candidate {
  id                String   @id @default(cuid())
  firstName         String   @map("first_name")
  lastName          String   @map("last_name")
  email             String   @unique
  phone             String?
  location          Json? // Location interface
  willingToRelocate Boolean? @map("willing_to_relocate")
  workAuthorization String?  @map("work_authorization")
  linkedinUrl       String?  @map("linkedin_url")
  portfolioUrl      String?  @map("portfolio_url")
  websiteUrl        String?  @map("website_url")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  applications Application[]

  @@map("candidates")
}

model Application {
  id                     String    @id @default(cuid())
  jobId                  String    @map("job_id")
  candidateId            String    @map("candidate_id")
  status                 String    @default("applied")
  submittedAt            DateTime? @map("submitted_at")
  candidateInfo          Json      @map("candidate_info") // CandidateInfo interface
  professionalInfo       Json?     @map("professional_info") // ProfessionalInfo interface
  documents              Json?     // ApplicationDocuments interface
  customAnswers          Json?     @map("custom_answers") // Record<string, CustomAnswer>
  metadata               Json?     // ApplicationMetadata interface
  scoring                Json?     // ApplicationScoring interface
  activity               Json[]    // ApplicationActivity[] interface
  reviewNotes            String?   @map("review_notes")
  reviewedById           String?   @map("reviewed_by_id")
  reviewedAt             DateTime? @map("reviewed_at")
  tags                   String[]
  lastContactDate        DateTime? @map("last_contact_date")
  nextFollowupDate       DateTime? @map("next_followup_date")
  communicationHistory   String[]  @map("communication_history")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")

  // Relations
  job        Job         @relation(fields: [jobId], references: [id], onDelete: Cascade)
  candidate  Candidate   @relation(fields: [candidateId], references: [id], onDelete: Cascade)
  reviewedBy User?       @relation("ApplicationReviewer", fields: [reviewedById], references: [id])
  interviews Interview[]
  documents  Document[]
  mlPredictions MLPrediction[]
  skillExtractions SkillExtraction[]

  @@map("applications")
}

model Interview {
  id            String    @id @default(cuid())
  applicationId String    @map("application_id")
  title         String
  type          String?
  scheduledDate DateTime? @map("scheduled_date")
  startTime     String?   @map("start_time")
  endTime       String?   @map("end_time")
  location      String?
  interviewers  String[] // Array of user IDs
  notes         String?
  feedback      Json? // Interview feedback structure
  status        String    @default("scheduled")
  createdById   String    @map("created_by_id")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Relations
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  createdBy   User        @relation("InterviewCreator", fields: [createdById], references: [id])

  @@map("interviews")
}

model Document {
  id            String    @id @default(cuid())
  applicationId String    @map("application_id")
  filename      String
  fileType      String?   @map("file_type")
  fileSize      Int?      @map("file_size")
  fileUrl       String    @map("file_url")
  documentType  String    @map("document_type") // 'resume', 'cover_letter', 'portfolio', etc.
  uploadedAt    DateTime  @default(now()) @map("uploaded_at")

  // Relations
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("documents")
}

model ApplicationFormSchema {
  id            String   @id @default(cuid())
  jobId         String   @map("job_id")
  title         String
  description   String?
  sections      Json     // FormSection[] interface
  settings      Json     // Form settings interface
  emailSettings Json     @map("email_settings") // Email settings interface
  version       Int      @default(1)
  createdById   String   @map("created_by_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  job Job @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@map("application_form_schemas")
}

model EmailTemplate {
  id        String   @id @default(cuid())
  name      String
  subject   String
  body      String
  type      String // 'confirmation', 'status_update', 'interview_invite', 'rejection', 'offer'
  variables String[] // Available template variables
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("email_templates")
}

// ML Models and Training Data
model MLModel {
  id            String   @id @default(cuid())
  name          String
  type          String   // 'candidate_scoring', 'job_matching', 'resume_parsing'
  version       String
  modelPath     String   @map("model_path")
  accuracy      Float?
  precision     Float?
  recall        Float?
  f1Score       Float?   @map("f1_score")
  trainingData  Json     @map("training_data") // Metadata about training dataset
  features      String[] // List of features used
  isActive      Boolean  @default(false) @map("is_active")
  trainedAt     DateTime @map("trained_at")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  predictions MLPrediction[]

  @@map("ml_models")
}

model MLPrediction {
  id            String   @id @default(cuid())
  modelId       String   @map("model_id")
  applicationId String   @map("application_id")
  predictionType String  @map("prediction_type") // 'priority_score', 'job_match', 'skill_extraction'
  inputFeatures Json     @map("input_features") // Features used for prediction
  prediction    Json     // Prediction results (score, confidence, etc.)
  confidence    Float?   // Confidence score (0-1)
  explanation   Json?    // Feature importance/explanation
  createdAt     DateTime @default(now()) @map("created_at")

  // Relations
  model       MLModel     @relation(fields: [modelId], references: [id], onDelete: Cascade)
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("ml_predictions")
}

model TrainingDataset {
  id          String   @id @default(cuid())
  name        String
  description String?
  source      String   // 'kaggle', 'internal', 'synthetic'
  datasetPath String   @map("dataset_path")
  features    String[] // List of features/columns
  targetVariable String @map("target_variable")
  recordCount Int      @map("record_count")
  version     String   @default("1.0")
  isActive    Boolean  @default(true) @map("is_active")
  metadata    Json?    // Additional dataset information
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("training_datasets")
}

model SkillExtraction {
  id            String   @id @default(cuid())
  applicationId String   @map("application_id")
  extractedSkills Json   @map("extracted_skills") // Array of skills with confidence scores
  skillCategories Json   @map("skill_categories") // Categorized skills (technical, soft, etc.)
  experienceLevel String? @map("experience_level") // Predicted experience level
  confidence      Float?  // Overall extraction confidence
  method          String  @default("ml") // 'ml', 'nlp', 'manual'
  createdAt       DateTime @default(now()) @map("created_at")

  // Relations
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("skill_extractions")
}

// Notifications table for real-time notifications
model Notification {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  type      String   // 'new_application', 'status_change', 'system_alert', 'interview_reminder'
  title     String
  message   String
  isRead    Boolean  @default(false) @map("is_read")
  metadata  Json?    // Additional notification data
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// User settings table for enhanced preferences
model UserSettings {
  id                    String  @id @default(cuid())
  userId                String  @unique @map("user_id")
  emailNotifications    Boolean @default(true) @map("email_notifications")
  pushNotifications     Boolean @default(true) @map("push_notifications")
  browserNotifications  Boolean @default(true) @map("browser_notifications")
  newApplications       Boolean @default(true) @map("new_applications")
  interviewReminders    Boolean @default(true) @map("interview_reminders")
  systemUpdates         Boolean @default(false) @map("system_updates")
  weeklyReports         Boolean @default(true) @map("weekly_reports")
  theme                 String  @default("light") // 'light', 'dark'
  language              String  @default("en")
  timezone              String  @default("UTC")
  profileVisibility     String  @default("team") // 'public', 'team', 'private'
  activityTracking      Boolean @default(true) @map("activity_tracking")
  dataSharing           Boolean @default(false) @map("data_sharing")
  analyticsOptIn        Boolean @default(true) @map("analytics_opt_in")
  compactMode           Boolean @default(false) @map("compact_mode")
  sidebarCollapsed      Boolean @default(false) @map("sidebar_collapsed")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_settings")
}
