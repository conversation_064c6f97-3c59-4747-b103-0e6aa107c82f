import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

/**
 * Enhanced Button component for TalentSol ATS application
 * Includes ATS-specific variants and improved styling for icons
 */
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // ATS-specific variants
        "ats-blue":
          "bg-ats-blue text-white hover:bg-ats-dark-blue",
        "ats-light-blue":
          "bg-ats-light-blue text-white hover:bg-ats-blue",
        "ats-purple":
          "bg-ats-purple text-white hover:bg-ats-dark-purple",
        "ats-light-purple":
          "bg-ats-light-purple text-white hover:bg-ats-purple",
        // Subtle variants
        "ats-blue-subtle":
          "border border-ats-blue/30 bg-ats-blue/10 text-ats-blue hover:bg-ats-blue/20",
        "ats-purple-subtle":
          "border border-ats-purple/30 bg-ats-purple/10 text-ats-purple hover:bg-ats-purple/20",
        // Outline variants
        "ats-outline-blue":
          "border border-ats-blue text-ats-blue hover:bg-ats-blue hover:text-white",
        "ats-outline-purple":
          "border border-ats-purple text-ats-purple hover:bg-ats-purple hover:text-white",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
        // Additional sizes
        xs: "h-8 rounded-md px-2 text-xs",
        xl: "h-12 rounded-md px-10 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
