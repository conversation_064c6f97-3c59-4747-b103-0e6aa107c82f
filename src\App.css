/* App.css - Custom styles for TalentSol ATS Application */

/* Global styles */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Root container styling */
#root {
  width: 100%;
  margin: 0 auto;
}

/* Logo styling */
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em rgba(59, 130, 246, 0.6)); /* Using ats-blue with opacity */
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em rgba(138, 112, 214, 0.6)); /* Using ats-purple with opacity */
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation utility classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-in-out;
}

/* Logo animation for reduced motion preferences */
@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

/* ATS specific utility classes */
.ats-card-hover {
  transition: all 0.2s ease-in-out;
}

.ats-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom scrollbar */
.ats-custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ats-custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ats-custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 4px;
}

.ats-custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a0a0a0;
}

/* Page transition effects */
.page-enter {
  opacity: 0;
}

.page-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

/* Focus styles that match the ATS theme */
.ats-focus-blue:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.ats-focus-purple:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(138, 112, 214, 0.5);
}

/* Gradient backgrounds */
.ats-gradient-blue {
  background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%);
}

.ats-gradient-purple {
  background: linear-gradient(135deg, #8A70D6 0%, #9b87f5 100%);
}

/* Text gradients */
.ats-text-gradient-blue {
  background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.ats-text-gradient-purple {
  background: linear-gradient(135deg, #8A70D6 0%, #9b87f5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    font-size: 12pt;
    color: #000;
    background: #fff;
  }
}

/* Card component styling */
.card {
  padding: 2em;
  border-radius: var(--radius);
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
}

/* Documentation link styling */
.read-the-docs {
  color: hsl(var(--muted-foreground));
  margin-top: 1.5rem;
  font-size: 0.875rem;
}
