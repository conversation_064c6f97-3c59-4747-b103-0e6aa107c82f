import * as React from "react"
import * as TogglePrimitive from "@radix-ui/react-toggle"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

/**
 * Enhanced Toggle component for TalentSol ATS application
 * Includes ATS-specific variants and improved styling
 */

const toggleVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",
  {
    variants: {
      variant: {
        default: "bg-transparent",
        outline:
          "border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",
        // ATS-specific variants
        "ats-blue":
          "bg-transparent hover:bg-ats-blue/10 hover:text-ats-blue data-[state=on]:bg-ats-blue data-[state=on]:text-white",
        "ats-light-blue":
          "bg-transparent hover:bg-ats-light-blue/10 hover:text-ats-light-blue data-[state=on]:bg-ats-light-blue data-[state=on]:text-white",
        "ats-purple":
          "bg-transparent hover:bg-ats-purple/10 hover:text-ats-purple data-[state=on]:bg-ats-purple data-[state=on]:text-white",
        "ats-light-purple":
          "bg-transparent hover:bg-ats-light-purple/10 hover:text-ats-light-purple data-[state=on]:bg-ats-light-purple data-[state=on]:text-white",
        // Subtle variants
        "ats-blue-subtle":
          "bg-transparent hover:bg-ats-blue/5 hover:text-ats-blue data-[state=on]:bg-ats-blue/10 data-[state=on]:text-ats-blue",
        "ats-purple-subtle":
          "bg-transparent hover:bg-ats-purple/5 hover:text-ats-purple data-[state=on]:bg-ats-purple/10 data-[state=on]:text-ats-purple",
      },
      size: {
        default: "h-10 px-3",
        sm: "h-9 px-2.5",
        lg: "h-11 px-5",
        // Additional sizes
        xs: "h-8 px-2 text-xs",
        xl: "h-12 px-6 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const Toggle = React.forwardRef<
  React.ElementRef<typeof TogglePrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &
    VariantProps<typeof toggleVariants>
>(({ className, variant, size, ...props }, ref) => (
  <TogglePrimitive.Root
    ref={ref}
    className={cn(toggleVariants({ variant, size, className }))}
    {...props}
  />
))

Toggle.displayName = TogglePrimitive.Root.displayName

export { Toggle, toggleVariants }
