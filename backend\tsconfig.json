{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "resolveJsonModule": true, "isolatedModules": true, "allowImportingTsExtensions": false, "noEmit": false, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"], "@/middleware/*": ["./middleware/*"], "@/routes/*": ["./routes/*"], "@/services/*": ["./services/*"], "@/controllers/*": ["./controllers/*"]}}, "include": ["src/**/*", "prisma/seed.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}